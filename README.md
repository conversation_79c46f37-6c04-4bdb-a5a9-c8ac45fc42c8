# Curio Admin Dashboard

现代化的管理后台界面，基于 React、Vite 和 Shadcn UI 构建。注重响应式设计和可访问性。

![alt text](public/images/shadcn-admin.png)

Curio Admin 是一个功能完整的管理后台模板，提供了丰富的组件和页面示例，适合快速构建现代化的管理系统。

## 功能特性

- 🌓 明暗主题切换
- 📱 响应式设计
- ♿ 无障碍访问
- 🎯 内置侧边栏组件
- 🔍 全局搜索命令
- 📄 10+ 页面示例
- 🧩 自定义组件
- 🌐 RTL 支持

<details>
<summary>Customized Components (click to expand)</summary>

This project uses Shadcn UI components, but some have been slightly modified for better RTL (Right-to-Left) support and other improvements. These customized components differ from the original Shadcn UI versions.

If you want to update components using the Shadcn CLI (e.g., `npx shadcn@latest add <component>`), it's generally safe for non-customized components. For the listed customized ones, you may need to manually merge changes to preserve the project's modifications and avoid overwriting RTL support or other updates.

> If you don't require RTL support, you can safely update the 'RTL Updated Components' via the Shadcn CLI, as these changes are primarily for RTL compatibility. The 'Modified Components' may have other customizations to consider.

### Modified Components

- scroll-area
- sonner
- separator

### RTL Updated Components

- alert-dialog
- calendar
- command
- dialog
- dropdown-menu
- select
- table
- sheet
- sidebar
- switch

**Notes:**

- **Modified Components**: These have general updates, potentially including RTL adjustments.
- **RTL Updated Components**: These have specific changes for RTL language support (e.g., layout, positioning).
- For implementation details, check the source files in `src/components/ui/`.
- All other Shadcn UI components in the project are standard and can be safely updated via the CLI.

</details>

## 技术栈

**UI 框架:** [ShadcnUI](https://ui.shadcn.com) (TailwindCSS + RadixUI)

**构建工具:** [Vite](https://vitejs.dev/)

**路由管理:** [TanStack Router](https://tanstack.com/router/latest)

**类型检查:** [TypeScript](https://www.typescriptlang.org/)

**代码规范:** [Eslint](https://eslint.org/) & [Prettier](https://prettier.io/)

**图标库:** [Lucide Icons](https://lucide.dev/icons/), [Tabler Icons](https://tabler.io/icons)

**身份验证:** [Clerk](https://go.clerk.com/GttUAaK)

## 本地运行

克隆项目

```bash
  git clone <your-repository-url>
```

进入项目目录

```bash
  cd curio-admin
```

安装依赖

```bash
  pnpm install
```

启动开发服务器

```bash
  pnpm run dev
```

## Sponsoring this project ❤️

If you find this project helpful or use this in your own work, consider [sponsoring me](https://github.com/sponsors/satnaing) to support development and maintenance. You can [buy me a coffee](https://buymeacoffee.com/satnaing) as well. Don’t worry, every penny helps. Thank you! 🙏

For questions or sponsorship inquiries, feel free to reach out at [<EMAIL>](mailto:<EMAIL>).

### Current Sponsor

- [Clerk](https://go.clerk.com/GttUAaK) - for backing the implementation of Clerk in this project

## Author

Crafted with 🤍 by [@satnaing](https://github.com/satnaing)

## License

Licensed under the [MIT License](https://choosealicense.com/licenses/mit/)
