import { type SVGProps } from 'react'

export function IconSidebarInset(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      data-name='icon-sidebar-inset'
      xmlns='http://www.w3.org/2000/svg'
      viewBox='0 0 79.86 51.14'
      {...props}
    >
      <rect
        x={23.39}
        y={5.57}
        width={50.22}
        height={40}
        rx={2}
        ry={2}
        opacity={0.2}
        strokeLinecap='round'
        strokeMiterlimit={10}
      />
      <path
        fill='none'
        opacity={0.72}
        strokeLinecap='round'
        strokeMiterlimit={10}
        strokeWidth='2px'
        d='M5.08 17.05L17.31 17.05'
      />
      <path
        fill='none'
        opacity={0.48}
        strokeLinecap='round'
        strokeMiterlimit={10}
        strokeWidth='2px'
        d='M5.08 24.25L15.6 24.25'
      />
      <path
        fill='none'
        opacity={0.55}
        strokeLinecap='round'
        strokeMiterlimit={10}
        strokeWidth='2px'
        d='M5.08 20.54L14.46 20.54'
      />
      <g strokeLinecap='round' strokeMiterlimit={10}>
        <circle cx={7.04} cy={9.57} r={2.54} opacity={0.8} />
        <path
          fill='none'
          opacity={0.8}
          strokeWidth='2px'
          d='M11.59 8.3L17.31 8.3'
        />
        <path fill='none' opacity={0.6} d='M11.38 10.95L16.44 10.95' />
      </g>
    </svg>
  )
}
