import { type SVGProps } from 'react'

export function IconLayoutFull(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      data-name='icon-layout-full'
      xmlns='http://www.w3.org/2000/svg'
      viewBox='0 0 79.86 51.14'
      {...props}
    >
      <path
        fill='none'
        opacity={0.75}
        strokeLinecap='round'
        strokeMiterlimit={10}
        strokeWidth='3px'
        d='M6.85 14.49L15.02 14.49'
      />
      <rect
        x={5.84}
        y={18.39}
        width={25.6}
        height={2.73}
        rx={0.64}
        ry={0.64}
        opacity={0.5}
        strokeLinecap='round'
        strokeMiterlimit={10}
      />
      <rect
        x={5.84}
        y={5.89}
        width={68.26}
        height={2.73}
        rx={0.64}
        ry={0.64}
        opacity={0.9}
        strokeLinecap='round'
        strokeMiterlimit={10}
      />
      <rect
        x={5.84}
        y={24.22}
        width={37.71}
        height={19.95}
        rx={2.11}
        ry={2.11}
        opacity={0.4}
        strokeLinecap='round'
        strokeMiterlimit={10}
      />
      <g strokeLinecap='round' strokeMiterlimit={10}>
        <rect
          x={59.05}
          y={38.15}
          width={2.01}
          height={3.42}
          rx={0.33}
          ry={0.33}
          opacity={0.32}
        />
        <rect
          x={54.78}
          y={34.99}
          width={2.01}
          height={6.58}
          rx={0.33}
          ry={0.33}
          opacity={0.44}
        />
        <rect
          x={63.17}
          y={32.86}
          width={2.01}
          height={8.7}
          rx={0.33}
          ry={0.33}
          opacity={0.53}
        />
        <rect
          x={67.54}
          y={29.17}
          width={2.01}
          height={12.4}
          rx={0.33}
          ry={0.33}
          opacity={0.66}
        />
      </g>
      <g opacity={0.5}>
        <circle cx={62.16} cy={18.63} r={7.5} />
        <path d='M62.16 11.63c3.86 0 7 3.14 7 7s-3.14 7-7 7-7-3.14-7-7 3.14-7 7-7m0-1c-4.42 0-8 3.58-8 8s3.58 8 8 8 8-3.58 8-8-3.58-8-8-8z' />
      </g>
      <g opacity={0.74}>
        <path d='M63.04 18.13l3.38-5.67c.93.64 1.7 1.48 2.26 2.47.56.98.89 2.08.96 3.21h-6.6z' />
        <path d='M66.57 13.19a6.977 6.977 0 012.52 4.44h-5.17l2.65-4.44m-.31-1.43l-4.1 6.87h8c0-1.39-.36-2.75-1.04-3.95a8.007 8.007 0 00-2.86-2.92z' />
      </g>
    </svg>
  )
}
